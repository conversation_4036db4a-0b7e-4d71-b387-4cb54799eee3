[build]
  command = "npm run build-netlify"
  publish = "dist"

[[headers]]
  for = "/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With, Accept, Origin, Referer, User-Agent"
    Access-Control-Max-Age = "86400"
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/*.m3u8"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, OPTIONS"
    Access-Control-Allow-Headers = "Range, Accept, Content-Type"
    Content-Type = "application/x-mpegURL"

[[headers]]
  for = "/*.ts"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, OPTIONS"
    Access-Control-Allow-Headers = "Range, Accept, Content-Type"
    Content-Type = "video/mp2t"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
