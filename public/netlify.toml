# 備用 Netlify 配置文件 - 手機優化版本
# 專門為解決手機 CORS 問題而設計

[build]
  command = "npm run build-netlify"
  publish = "dist"

# 全域 CORS 頭設置
[[headers]]
  for = "/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With, Accept, Origin, Referer, User-Agent, Range"
    Access-Control-Max-Age = "86400"
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# HLS 播放清單文件專用設置
[[headers]]
  for = "/*.m3u8"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, OPTIONS"
    Access-Control-Allow-Headers = "Range, Accept, Content-Type, User-Agent, Referer"
    Content-Type = "application/x-mpegURL"
    Cache-Control = "no-cache, no-store, must-revalidate"

# HLS 影片片段專用設置
[[headers]]
  for = "/*.ts"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, OPTIONS"
    Access-Control-Allow-Headers = "Range, Accept, Content-Type, User-Agent, Referer"
    Content-Type = "video/mp2t"
    Cache-Control = "public, max-age=3600"

# M3U 播放清單文件
[[headers]]
  for = "/*.m3u"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, OPTIONS"
    Access-Control-Allow-Headers = "Range, Accept, Content-Type, User-Agent, Referer"
    Content-Type = "audio/x-mpegurl"
    Cache-Control = "no-cache, no-store, must-revalidate"

# 代理重定向 - 處理 CORS 問題
[[redirects]]
  from = "/proxy/*"
  to = "https://api.allorigins.win/raw?url=:splat"
  status = 200
  force = true
  headers = {Access-Control-Allow-Origin = "*"}

# 備用代理
[[redirects]]
  from = "/proxy2/*"
  to = "https://corsproxy.io/?:splat"
  status = 200
  force = true
  headers = {Access-Control-Allow-Origin = "*"}

# SPA 路由處理
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
