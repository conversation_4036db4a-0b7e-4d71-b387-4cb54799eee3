import React, { useState } from 'react';
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
  DevicePhoneMobileIcon,
  WifiIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';

interface MobilePlaybackGuideProps {
  error?: string;
  onRetry?: () => void;
  onDismiss?: () => void;
}

export const MobilePlaybackGuide: React.FC<MobilePlaybackGuideProps> = ({
  error,
  onRetry,
  onDismiss
}) => {
  const [showDetailedGuide, setShowDetailedGuide] = useState(false);

  const commonSolutions = [
    {
      icon: <WifiIcon className="h-6 w-6 text-blue-500" />,
      title: "檢查網路連線",
      description: "確保您的網路連線穩定，建議使用 WiFi 而非行動數據"
    },
    {
      icon: <DevicePhoneMobileIcon className="h-6 w-6 text-green-500" />,
      title: "重新整理頁面",
      description: "下拉頁面重新整理，或關閉瀏覽器重新開啟"
    },
    {
      icon: <PlayIcon className="h-6 w-6 text-purple-500" />,
      title: "手動點擊播放",
      description: "手機瀏覽器需要用戶互動才能播放影片，請點擊播放按鈕"
    }
  ];

  const troubleshootingSteps = [
    "確認網路連線正常",
    "嘗試切換到 WiFi 網路",
    "清除瀏覽器快取和 Cookie",
    "重新啟動瀏覽器應用程式",
    "嘗試使用其他瀏覽器（如 Chrome、Safari）",
    "檢查是否有廣告攔截器干擾",
    "確認頻道來源是否正常運作"
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-6 w-6 text-orange-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              播放問題解決指南
            </h3>
          </div>
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-5 w-5" />
            </Button>
          )}
        </div>

        {/* 錯誤訊息 */}
        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  <strong>錯誤訊息：</strong> {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 快速解決方案 */}
        <div className="p-4">
          <h4 className="text-md font-medium text-gray-900 mb-3">
            快速解決方案
          </h4>
          <div className="space-y-3">
            {commonSolutions.map((solution, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                {solution.icon}
                <div>
                  <h5 className="text-sm font-medium text-gray-900">
                    {solution.title}
                  </h5>
                  <p className="text-sm text-gray-600 mt-1">
                    {solution.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 重試按鈕 */}
        {onRetry && (
          <div className="px-4 pb-4">
            <Button
              onClick={onRetry}
              className="w-full"
              leftIcon={<PlayIcon className="h-4 w-4" />}
            >
              重新嘗試播放
            </Button>
          </div>
        )}

        {/* 詳細故障排除 */}
        <div className="px-4 pb-4">
          <Button
            variant="outline"
            onClick={() => setShowDetailedGuide(!showDetailedGuide)}
            className="w-full"
            leftIcon={<InformationCircleIcon className="h-4 w-4" />}
          >
            {showDetailedGuide ? '隱藏' : '顯示'}詳細故障排除步驟
          </Button>

          {showDetailedGuide && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h5 className="text-sm font-medium text-blue-900 mb-3">
                詳細故障排除步驟
              </h5>
              <ol className="text-sm text-blue-800 space-y-2">
                {troubleshootingSteps.map((step, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                      {index + 1}
                    </span>
                    {step}
                  </li>
                ))}
              </ol>
            </div>
          )}
        </div>

        {/* 手機專用提示 */}
        <div className="p-4 bg-yellow-50 border-t">
          <div className="flex">
            <DevicePhoneMobileIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h5 className="text-sm font-medium text-yellow-800">
                手機播放提示
              </h5>
              <div className="text-sm text-yellow-700 mt-1 space-y-1">
                <p>• 手機瀏覽器對影片播放有較嚴格的限制</p>
                <p>• 建議使用 WiFi 網路以獲得更好的播放體驗</p>
                <p>• 某些頻道可能需要特定的瀏覽器才能正常播放</p>
                <p>• 如果問題持續，請嘗試使用電腦版瀏覽器</p>
              </div>
            </div>
          </div>
        </div>

        {/* 聯絡支援 */}
        <div className="p-4 border-t bg-gray-50">
          <p className="text-xs text-gray-600 text-center">
            如果問題仍然存在，這可能是頻道來源的問題。
            <br />
            請嘗試其他頻道或稍後再試。
          </p>
        </div>
      </div>
    </div>
  );
};
