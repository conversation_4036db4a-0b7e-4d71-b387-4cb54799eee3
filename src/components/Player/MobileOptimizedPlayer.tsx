import React, { useEffect, useRef, useCallback, useState } from 'react';
import Hls from 'hls.js';
import { usePlayerStore } from '../../stores/playerStore';
import { CorsProxy } from '../../utils/corsProxy';
import { MobilePlaybackGuide } from './MobilePlaybackGuide';
import type { M3UChannel } from '../../types';

interface MobileOptimizedPlayerProps {
  channel: M3UChannel;
  autoplay?: boolean;
  className?: string;
}

export const MobileOptimizedPlayer: React.FC<MobileOptimizedPlayerProps> = ({
  channel,
  autoplay = false,
  className = ''
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const retryCountRef = useRef(0);
  const maxRetries = 2; // 手機設備減少重試次數
  const [showGuide, setShowGuide] = useState(false);
  const [lastError, setLastError] = useState<string>('');

  const {
    isPlaying,
    volume,
    isMuted,
    setLoading,
    setError,
    clearError,
    setHlsInstance,
    destroyHlsInstance,
    setDuration,
    setCurrentTime,
    play,
    pause
  } = usePlayerStore();

  // 手機專用的重試機制
  const retryPlayback = useCallback(() => {
    if (retryCountRef.current < maxRetries) {
      retryCountRef.current++;
      console.log(`📱 手機重試播放，第 ${retryCountRef.current} 次`);

      setTimeout(() => {
        const video = videoRef.current;
        if (video && channel.url) {
          initializePlayer();
        }
      }, 2000 * retryCountRef.current); // 手機設備使用更長的重試間隔
    } else {
      const errorMsg = '播放失敗：網路連線不穩定，請檢查網路後重試';
      setError(errorMsg);
      setLastError(errorMsg);
      setLoading(false);
      setShowGuide(true); // 顯示指導
    }
  }, [channel.url]);

  // 手機專用的播放器初始化
  const initializePlayer = useCallback(async () => {
    const video = videoRef.current;
    if (!video || !channel.url) return;

    setLoading(true);
    clearError();

    console.log('📱 初始化手機播放器');
    console.log('📺 頻道:', channel.name);
    console.log('🔗 URL:', channel.url);

    try {
      // 首先嘗試使用代理獲取播放清單
      let playlistUrl = channel.url;
      
      if (CorsProxy.needsProxy(channel.url)) {
        console.log('🌐 檢測到需要代理，使用 CORS 代理');
        try {
          const response = await CorsProxy.fetchWithProxy(channel.url);
          const blob = await response.blob();
          playlistUrl = URL.createObjectURL(blob);
          console.log('✅ 代理獲取成功，使用 Blob URL');
        } catch (proxyError) {
          console.warn('⚠️ 代理失敗，嘗試直接播放:', proxyError);
          // 繼續使用原始 URL
        }
      }

      // 檢查是否支援 HLS
      if (Hls.isSupported()) {
        console.log('📱 使用 HLS.js (手機優化版本)');
        
        const hls = new Hls({
          // 手機專用的保守配置
          debug: false,
          enableWorker: false, // 手機設備關閉 Worker
          lowLatencyMode: false,
          backBufferLength: 10, // 減少後緩衝
          maxBufferLength: 15, // 減少最大緩衝
          maxMaxBufferLength: 30, // 減少最大最大緩衝
          maxBufferSize: 20 * 1000 * 1000, // 20MB
          maxBufferHole: 0.2,
          highBufferWatchdogPeriod: 1,
          nudgeOffset: 0.1,
          nudgeMaxRetry: 2,
          maxLoadingDelay: 2,
          maxFragLookUpTolerance: 0.1,
          liveSyncDurationCount: 2,
          liveMaxLatencyDurationCount: 5,
          // 手機專用的載入器設置
          xhrSetup: (xhr: XMLHttpRequest, url: string) => {
            xhr.timeout = 10000; // 10秒超時
            xhr.setRequestHeader('Accept', 'application/x-mpegURL, application/vnd.apple.mpegurl, */*');
            xhr.setRequestHeader('Cache-Control', 'no-cache');
          }
        });

        setHlsInstance(hls);
        hls.loadSource(playlistUrl);
        hls.attachMedia(video);

        // 手機專用事件處理
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          console.log('📱 HLS manifest 解析完成 (手機)');
          setLoading(false);
          retryCountRef.current = 0;

          // 手機設備自動選擇較低品質
          if (hls.levels.length > 1) {
            const lowQualityLevel = Math.min(1, hls.levels.length - 1);
            hls.currentLevel = lowQualityLevel;
            console.log(`📱 自動選擇較低品質: Level ${lowQualityLevel}`);
          }

          if (autoplay) {
            // 手機設備需要用戶互動才能自動播放
            video.play().catch(error => {
              console.warn('📱 手機自動播放失敗 (需要用戶互動):', error);
              setError('請點擊播放按鈕開始觀看');
            });
          }
        });

        hls.on(Hls.Events.ERROR, (_, data) => {
          console.error('📱 手機 HLS 錯誤:', data);

          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                const networkError = '網路連線問題，請檢查網路後重試';
                setError(networkError);
                setLastError(networkError);
                setShowGuide(true);
                retryPlayback();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.log('📱 嘗試恢復媒體錯誤');
                try {
                  hls.recoverMediaError();
                } catch {
                  const mediaError = '影片格式不相容';
                  setError(mediaError);
                  setLastError(mediaError);
                  setShowGuide(true);
                  retryPlayback();
                }
                break;
              default:
                const generalError = '播放失敗，請重試';
                setError(generalError);
                setLastError(generalError);
                setLoading(false);
                setShowGuide(true);
                break;
            }
          }
        });

        return () => hls.destroy();

      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // iOS Safari 原生支援
        console.log('📱 使用 iOS Safari 原生 HLS');
        video.src = playlistUrl;
        setLoading(false);
        retryCountRef.current = 0;

        if (autoplay) {
          video.play().catch(error => {
            console.warn('📱 iOS 自動播放失敗:', error);
            setError('請點擊播放按鈕開始觀看');
          });
        }

      } else {
        // 嘗試直接播放
        console.log('📱 嘗試直接播放');
        video.src = playlistUrl;
        setLoading(false);
        retryCountRef.current = 0;

        if (autoplay) {
          video.play().catch(error => {
            console.warn('📱 直接播放失敗:', error);
            setError('請點擊播放按鈕開始觀看');
          });
        }
      }

    } catch (error) {
      console.error('📱 播放器初始化失敗:', error);
      setError('初始化失敗，請重試');
      setLoading(false);
    }
  }, [channel.url, autoplay, setLoading, clearError, setHlsInstance, setError, retryPlayback]);

  useEffect(() => {
    retryCountRef.current = 0;
    initializePlayer();
    return () => destroyHlsInstance();
  }, [channel.url, initializePlayer, destroyHlsInstance]);

  // 播放控制
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.play().catch(error => {
        console.error('📱 播放失敗:', error);
        setError('播放失敗，請重試');
      });
    } else {
      video.pause();
    }
  }, [isPlaying, setError]);

  // 音量控制
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;
    video.volume = isMuted ? 0 : volume;
    video.muted = isMuted;
  }, [volume, isMuted]);

  // 事件處理
  const handleLoadedMetadata = () => {
    const video = videoRef.current;
    if (video) {
      setDuration(video.duration);
      console.log('📱 影片元數據已載入');
    }
  };

  const handleTimeUpdate = () => {
    const video = videoRef.current;
    if (video) setCurrentTime(video.currentTime);
  };

  const handlePlay = () => {
    console.log('📱 開始播放');
    play();
  };

  const handlePause = () => {
    console.log('📱 暫停播放');
    pause();
  };

  const handleError = () => {
    console.error('📱 影片錯誤');
    const errorMsg = '播放錯誤，請重試';
    setError(errorMsg);
    setLastError(errorMsg);
    setShowGuide(true);
    retryPlayback();
  };

  const handleWaiting = () => {
    console.log('📱 緩衝中...');
    setLoading(true);
  };

  const handleCanPlay = () => {
    console.log('📱 可以播放');
    setLoading(false);
  };

  return (
    <>
      <video
        ref={videoRef}
        className={`w-full h-full bg-black ${className}`}
        controls
        playsInline
        preload="metadata" // 手機設備使用 metadata
        crossOrigin="anonymous"
        onLoadedMetadata={handleLoadedMetadata}
        onTimeUpdate={handleTimeUpdate}
        onPlay={handlePlay}
        onPause={handlePause}
        onError={handleError}
        onWaiting={handleWaiting}
        onCanPlay={handleCanPlay}
        poster={channel.logo}
        // 手機專用屬性
        autoPlay={false}
        muted={false}
        webkit-playsinline="true"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="true"
        x5-video-orientation="portraint"
        style={{
          objectFit: 'contain',
          backgroundColor: '#000',
          maxHeight: '70vh' // 手機設備限制高度
        }}
      >
        您的瀏覽器不支援影片播放。
      </video>

      {/* 手機播放指導 */}
      {showGuide && (
        <MobilePlaybackGuide
          error={lastError}
          onRetry={() => {
            setShowGuide(false);
            retryCountRef.current = 0;
            initializePlayer();
          }}
          onDismiss={() => setShowGuide(false)}
        />
      )}
    </>
  );
};
