// CORS 代理工具
export class CorsProxy {
  private static proxies = [
    'https://api.allorigins.win/raw?url=',
    'https://cors-anywhere.herokuapp.com/',
    'https://thingproxy.freeboard.io/fetch/',
    'https://api.codetabs.com/v1/proxy?quest=',
    // 新增更多可靠的代理服務
    'https://corsproxy.io/?',
    'https://proxy.cors.sh/',
    'https://cors.bridged.cc/',
    'https://yacdn.org/proxy/',
  ];

  private static currentProxyIndex = 0;

  /**
   * 獲取當前代理 URL
   */
  private static getCurrentProxy(): string {
    return this.proxies[this.currentProxyIndex];
  }

  /**
   * 切換到下一個代理
   */
  private static switchToNextProxy(): void {
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
    console.log(`🔄 切換到代理: ${this.getCurrentProxy()}`);
  }

  /**
   * 檢測是否為手機設備
   */
  private static isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * 使用代理獲取資源
   */
  static async fetchWithProxy(url: string, options: RequestInit = {}): Promise<Response> {
    const maxRetries = this.proxies.length;
    let lastError: Error | null = null;
    const isMobile = this.isMobileDevice();

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const proxy = this.getCurrentProxy();
        const proxyUrl = `${proxy}${encodeURIComponent(url)}`;

        console.log(`🌐 嘗試使用代理 ${attempt + 1}/${maxRetries}: ${proxy}`);
        console.log(`📡 請求 URL: ${proxyUrl}`);
        console.log(`📱 設備類型: ${isMobile ? '手機' : '桌面'}`);

        // 手機設備使用更寬鬆的請求設置
        const fetchOptions: RequestInit = {
          ...options,
          headers: {
            'Accept': 'text/plain, application/x-mpegURL, application/vnd.apple.mpegurl, */*',
            'User-Agent': isMobile ?
              'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1' :
              'TVBOX/1.0',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            ...options.headers,
          },
          // 手機設備使用更短的超時時間
          signal: AbortSignal.timeout(isMobile ? 15000 : 30000),
        };

        const response = await fetch(proxyUrl, fetchOptions);

        if (response.ok) {
          console.log(`✅ 代理請求成功: ${proxy}`);
          return response;
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.warn(`❌ 代理失敗 ${attempt + 1}/${maxRetries}:`, error);
        lastError = error as Error;

        if (attempt < maxRetries - 1) {
          this.switchToNextProxy();
          // 手機設備使用更短的重試間隔
          const retryDelay = isMobile ? 500 : 1000;
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    // 所有代理都失敗，嘗試直接請求
    console.log('🔄 所有代理都失敗，嘗試直接請求...');
    try {
      const directOptions: RequestInit = {
        ...options,
        headers: {
          'Accept': 'text/plain, application/x-mpegURL, application/vnd.apple.mpegurl, */*',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          ...options.headers,
        },
        signal: AbortSignal.timeout(isMobile ? 10000 : 20000),
      };

      const response = await fetch(url, directOptions);
      if (response.ok) {
        console.log('✅ 直接請求成功');
        return response;
      }
    } catch (error) {
      console.warn('❌ 直接請求也失敗:', error);
    }

    throw new Error(`所有請求方式都失敗。最後錯誤: ${lastError?.message}`);
  }

  /**
   * 檢查 URL 是否需要代理
   */
  static needsProxy(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const currentOrigin = window.location.origin;
      
      // 如果是同源請求，不需要代理
      if (urlObj.origin === currentOrigin) {
        return false;
      }

      // 檢查是否是已知的需要代理的域名
      const needsProxyDomains = [
        'files.catbox.moe',
        'raw.githubusercontent.com',
        'gist.githubusercontent.com',
        'pastebin.com',
      ];

      return needsProxyDomains.some(domain => urlObj.hostname.includes(domain));
    } catch {
      return false;
    }
  }

  /**
   * 智能獲取資源（自動判斷是否需要代理）
   */
  static async smartFetch(url: string, options: RequestInit = {}): Promise<Response> {
    console.log(`🔍 智能請求: ${url}`);
    
    if (this.needsProxy(url)) {
      console.log('🌐 檢測到需要代理的 URL，使用代理請求');
      return this.fetchWithProxy(url, options);
    } else {
      console.log('📡 直接請求');
      return fetch(url, options);
    }
  }
}

// 導出便捷函數
export const fetchWithCorsProxy = CorsProxy.fetchWithProxy.bind(CorsProxy);
export const smartFetch = CorsProxy.smartFetch.bind(CorsProxy);
